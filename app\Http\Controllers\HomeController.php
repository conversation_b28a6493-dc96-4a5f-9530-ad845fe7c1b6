<?php

namespace App\Http\Controllers;

use App\Models\ArticleCategory;
use Illuminate\Http\Request;

class HomeController extends Controller
{
    public function index()
    {
        $types = ArticleCategory::whereNull('parent_id')->with('children.children')->get();
        
        $categoriesJs = [];
        foreach ($types as $type) {
            foreach ($type->children as $category) {
                $categoriesJs[$category->slug] = $category->children->map(function ($subcategory) {
                    return [
                        'title' => $subcategory->name,
                        'slug' => $subcategory->slug,
                        'href' => '#',
                        'icon_svg' => $subcategory->icon_svg,
                    ];
                });
            }
        }

        return view('welcome', [
            'types' => $types,
            'categoriesJs' => $categoriesJs,
        ]);
    }
} 