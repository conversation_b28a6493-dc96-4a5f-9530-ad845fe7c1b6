<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\ArticleCategory;

class ArticleCategorySeeder extends Seeder
{
    public function run(): void
    {
        // Создаем родительские категории
        $type1Id = Str::uuid();
        $type2Id = Str::uuid();

        ArticleCategory::create([
            'id' => $type1Id,
            'name' => 'Диабет первого типа',
            'slug' => 'diabet-pervogo-tipa',
            'parent_id' => null,
        ]);

        ArticleCategory::create([
            'id' => $type2Id,
            'name' => 'Диабет второго типа',
            'slug' => 'diabet-vtorogo-tipa',
            'parent_id' => null,
        ]);

        // Подкатегории для Диабета первого типа
        $type1Categories = [
            ['name' => 'Питание', 'slug' => 'pitanie-1'],
            ['name' => 'Инсулинотерапия', 'slug' => 'insulinoterapiya'],
            ['name' => 'Гипогликемия', 'slug' => 'gipoglikemiya'],
            ['name' => 'Самоконтроль', 'slug' => 'samokontrol'],
            ['name' => 'Детям и подросткам', 'slug' => 'detyam-i-podrostkam'],
        ];

        $type1Subcategories = [];
        foreach ($type1Categories as $category) {
            $id = Str::uuid();
            ArticleCategory::create([
                'id' => $id,
                'name' => $category['name'],
                'slug' => $category['slug'],
                'parent_id' => $type1Id,
            ]);
            $type1Subcategories[$category['slug']] = $id;
        }

        // Подкатегории для Диабета второго типа
        $type2Categories = [
            ['name' => 'Питание', 'slug' => 'pitanie-2'],
            ['name' => 'Таблетки', 'slug' => 'tabletki'],
            ['name' => 'Физическая активность', 'slug' => 'fizicheskaya-aktivnost'],
            ['name' => 'Контроль веса', 'slug' => 'kontrol-vesa'],
            ['name' => 'Сопутствующие заболевания', 'slug' => 'soputstvuyushie-zabolevaniya'],
        ];

        $type2Subcategories = [];
        foreach ($type2Categories as $category) {
            $id = Str::uuid();
            ArticleCategory::create([
                'id' => $id,
                'name' => $category['name'],
                'slug' => $category['slug'],
                'parent_id' => $type2Id,
            ]);
            $type2Subcategories[$category['slug']] = $id;
        }

        // Подкатегории второго уровня для Диабета первого типа
        $type1SecondLevel = [
            'pitanie-1' => [
                ['name' => 'Расчет хлебных единиц (ХЕ)', 'slug' => 'raschet-he', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>'],
                ['name' => 'Низкоуглеводная диета', 'slug' => 'nizkouglevodnaya-dieta', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 6h18M3 12h18M3 18h18"></path></svg>'],
                ['name' => 'Гликемический индекс продуктов', 'slug' => 'glikemicheskiy-indeks', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16 8v8m-4-5v5M8 8v8m-4-8h18"></path></svg>'],
                ['name' => 'Планирование питания и меню', 'slug' => 'planirovanie-pitaniya', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"></path></svg>'],
            ],
            'insulinoterapiya' => [
                ['name' => 'Типы инсулинов и их действие', 'slug' => 'tipy-insulinov', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"></path></svg>'],
                ['name' => 'Техника инъекций', 'slug' => 'tehnika-inekciy', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21.75 8.25F17.25 12.75 12.75 17.25 8.25 21.75m13.5-13.5L12 12m9.75-3.75L12 12M12 12L2.25 2.25M12 12l9.75 9.75"></path></svg>'],
                ['name' => 'Расчет дозы инсулина', 'slug' => 'raschet-dozy-insulina', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15.75 15.75l-2.489-2.489m0 0L11.011 11.011m2.25-2.25L15.75 11.011M15.75 15.75l2.489 2.489m0 0L20.489 15.75m-2.25 2.25L15.75 20.489m2.25-2.25l2.489-2.489"></path></svg>'],
                ['name' => 'Инсулиновые помпы', 'slug' => 'insulinovye-pompy', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z"></path></svg>'],
            ],
            'gipoglikemiya' => [
                ['name' => 'Симптомы гипогликемии', 'slug' => 'simptomy-gipoglikemii', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z"></path></svg>'],
                ['name' => 'Что делать при гипогликемии', 'slug' => 'chto-delat-pri-gipoglikemii', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'],
                ['name' => 'Профилактика гипогликемии', 'slug' => 'profilaktika-gipoglikemii', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12.75L11.25 15 15 9.75m-3-7.036A11.959 11.959 0 013.598 6 11.99 11.99 0 003 9.749c0 5.592 3.824 10.29 9 11.622 5.176-1.332 9-6.03 9-11.622 0-1.31-.21-2.571-.598-3.751h-.152c-3.196 0-6.1-1.248-8.25-3.286z"></path></svg>'],
            ],
            'samokontrol' => [
                ['name' => 'Измерение глюкозы (глюкометры)', 'slug' => 'izmerenie-glukozy', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.5 12.75l6 6 9-13.5"></path></svg>'],
                ['name' => 'Целевые показатели сахара', 'slug' => 'celevye-pokazateli-sahara', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 10.5a3 3 0 11-6 0 3 3 0 016 0z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1115 0z"></path></svg>'],
                ['name' => 'Ведение дневника диабета', 'slug' => 'vedenie-dnevnika', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M16.862 4.487l1.687-1.688a1.875 1.875 0 112.652 2.652L10.582 16.07a4.5 4.5 0 01-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 011.13-1.897l8.932-8.931zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0115.75 21H5.25A2.25 2.25 0 013 18.75V8.25A2.25 2.25 0 015.25 6H10"></path></svg>'],
                ['name' => 'Контроль гликированного гемоглобина', 'slug' => 'glikirovanniy-gemoglobin', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.5 6a7.5 7.5 0 100 15 7.5 7.5 0 000-15z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.5 6.75h.008v.008H10.5V6.75z" stroke-width="2.5"></path><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 10.5v5.25"></path></svg>'],
            ],
            'detyam-i-podrostkam' => [
                ['name' => 'Диабет у детей: особенности', 'slug' => 'diabet-u-detey', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15.182 14.818l-6.364-6.364M15.182 14.818L18 12m-2.818 2.818l-2.828 2.829M18 12l-2.818-2.818M12 18l-2.828-2.828M12 6l-2.818 2.818"></path></svg>'],
                ['name' => 'Школа диабета для родителей', 'slug' => 'shkola-diabeta-dlya-roditeley', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.042A8.967 8.967 0 006 3.75c-1.052 0-2.062.18-3 .512v14.25A8.987 8.987 0 016 18c2.305 0 4.408.867 6 2.292m0-14.25a8.966 8.966 0 016-2.292c1.052 0 2.062.18 3 .512v14.25A8.987 8.987 0 0018 18a8.967 8.967 0 00-6 2.292m0-14.25v14.25"></path></svg>'],
                ['name' => 'Психологическая поддержка', 'slug' => 'psihologicheskaya-podderzhka', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 20.25c4.97 0 9-3.694 9-8.25s-4.03-8.25-9-8.25S3 7.006 3 11.55c0 4.556 4.03 8.25 9 8.25zM12 12.75a2.25 2.25 0 100-4.5 2.25 2.25 0 000 4.5z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 12.75c-2.43 3.3-6.23 5.5-10.5 5.5S.07 16.05 0 12.75"></path></svg>'],
            ],
        ];

        foreach ($type1SecondLevel as $parentSlug => $categories) {
            foreach ($categories as $category) {
                ArticleCategory::create([
                    'id' => Str::uuid(),
                    'name' => $category['name'],
                    'icon_svg' => $category['icon_svg'] ?? null,
                    'slug' => $category['slug'],
                    'parent_id' => $type1Subcategories[$parentSlug],
                ]);
            }
        }

        // Подкатегории второго уровня для Диабета второго типа
        $type2SecondLevel = [
            'pitanie-2' => [
                ['name' => 'Диета при Диабете 2 типа', 'slug' => 'dieta-pri-d2t', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg>'],
                ['name' => 'Контроль веса и питание', 'slug' => 'kontrol-vesa-pitanie', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.75 9.75h16.5v8.25a2.25 2.25 0 01-2.25 2.25H6a2.25 2.25 0 01-2.25-2.25V9.75z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.75 6.75h16.5v3H3.75z"></path></svg>'],
                ['name' => 'Продукты, которые можно и нельзя', 'slug' => 'produkty-mozhno-nelzya', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 12.75L11.25 15 15 9.75M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'],
            ],
            'tabletki' => [
                ['name' => 'Виды сахароснижающих препаратов', 'slug' => 'vidy-saharonizhayushih-preparatov', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7.5 10.5l-2.121 2.121a2.25 2.25 0 000 3.182l.182.182a2.25 2.25 0 010 3.182l-2.121 2.121M16.5 6.5l2.121-2.121a2.25 2.25 0 013.182 0l.182.182a2.25 2.25 0 003.182 0l2.121-2.121"></path></svg>'],
                ['name' => 'Побочные эффекты и взаимодействие', 'slug' => 'pobochnye-effekty-vzaimodeystvie', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z"></path></svg>'],
                ['name' => 'Правила приема препаратов', 'slug' => 'pravila-priema-preparatov', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'],
            ],
            'fizicheskaya-aktivnost' => [
                ['name' => 'Польза спорта при диабете', 'slug' => 'polza-sporta', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M4.5 12.75l6 6 9-13.5"></path></svg>'],
                ['name' => 'Рекомендации по видам активности', 'slug' => 'rekomendacii-vidy-aktivnosti', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path></svg>'],
                ['name' => 'Контроль сахара до и после тренировок', 'slug' => 'kontrol-sahara-trenirovki', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.5 6a7.5 7.5 0 100 15 7.5 7.5 0 000-15z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M10.5 6.75h.008v.008H10.5V6.75z" stroke-width="2.5"></path><path stroke-linecap="round" stroke-linejoin="round" d="M10.5 10.5v5.25"></path></svg>'],
            ],
            'kontrol-vesa' => [
                ['name' => 'Ожирение и Диабет 2 типа', 'slug' => 'ozhirenie-d2t', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z"></path></svg>'],
                ['name' => 'Стратегии снижения веса', 'slug' => 'strategii-snizheniya-vesa', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.25 18L9 11.25l4.306 4.307a11.95 11.95 0 015.814-5.517l2.74-1.22m0 0l-3.26-3.26m3.26 3.26l-3.26 3.26"></path></svg>'],
                ['name' => 'Роль диеты и спорта в контроле веса', 'slug' => 'rol-diety-sporta-ves', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6v6h4.5m4.5 0a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>'],
            ],
            'soputstvuyushie-zabolevaniya' => [
                ['name' => 'Осложнения диабета: обзор', 'slug' => 'oslozhneniya-diabeta-obzor', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 9v3.75m-9.303 3.376c-.866 1.5.217 3.374 1.948 3.374h14.71c1.73 0 2.813-1.874 1.948-3.374L13.949 3.378c-.866-1.5-3.032-1.5-3.898 0L2.697 16.126z"></path></svg>'],
                ['name' => 'Нейропатия: что нужно знать', 'slug' => 'neyropatiya', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 6.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 12.75a.75.75 0 110-1.5.75.75 0 010 1.5zM12 18.75a.75.75 0 110-1.5.75.75 0 010 1.5z"></path></svg>'],
                ['name' => 'Нефропатия и защита почек', 'slug' => 'nefropatiya', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M12 21a9.004 9.004 0 008.716-6.747M12 21a9.004 9.004 0 01-8.716-6.747M12 21c1.355 0 2.707-.158 4-.447m-4 .447c-1.293-.289-2.645-.447-4-.447m8.716-6.299c.145-.382.283-.77.411-1.168m-8.122 0c.128.398.266.786.411 1.168m7.701 0a9.03 9.03 0 01-1.76-1.63M12 15c-1.355 0-2.707-.158-4-.447m4 .447c-1.293-.289-2.645-.447-4-.447"></path></svg>'],
                ['name' => 'Ретинопатия: зрение при диабете', 'slug' => 'retinopatiya', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M2.036 12.322a1.012 1.012 0 010-.639C3.423 7.51 7.36 4.5 12 4.5c4.638 0 8.573 3.007 9.963 7.178.07.207.07.432 0 .639C20.577 16.49 16.64 19.5 12 19.5c-4.638 0-8.573-3.007-9.963-7.178z"></path><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path></svg>'],
                ['name' => 'Сердечно-сосудистые риски', 'slug' => 'serdechno-sosudistye-riski', 'icon_svg' => '<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M21 8.25c0-2.485-2.099-4.5-4.688-4.5-1.935 0-3.597 1.126-4.312 2.733-.715-1.607-2.377-2.733-4.313-2.733C5.1 3.75 3 5.765 3 8.25c0 7.22 9 12 9 12s9-4.78 9-12z"></path></svg>'],
            ],
        ];

        foreach ($type2SecondLevel as $parentSlug => $categories) {
            foreach ($categories as $category) {
                ArticleCategory::create([
                    'id' => Str::uuid(),
                    'name' => $category['name'],
                    'icon_svg' => $category['icon_svg'] ?? null,
                    'slug' => $category['slug'],
                    'parent_id' => $type2Subcategories[$parentSlug],
                ]);
            }
        }
    }
} 