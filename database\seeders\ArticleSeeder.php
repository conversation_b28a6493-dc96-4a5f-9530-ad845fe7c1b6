<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Str;
use App\Models\Article;
use App\Models\ArticleCategory;

class ArticleSeeder extends Seeder
{
    public function run(): void
    {
        // Получаем все категории второго уровня
        $categories = ArticleCategory::whereNotNull('parent_id')
            ->whereHas('parent', function ($query) {
                $query->whereNotNull('parent_id');
            })
            ->get();

        foreach ($categories as $category) {
            // Создаем 2-3 статьи для каждой категории
            $articlesCount = rand(2, 3);
            
            for ($i = 0; $i < $articlesCount; $i++) {
                $title = $this->generateArticleTitle($category->name, $i);
                $slug = Str::slug($title);
                
                Article::create([
                    'id' => Str::uuid(),
                    'title' => $title,
                    'slug' => $slug,
                    'content' => $this->generateContent(),
                    'article_category_id' => $category->id,
                    'is_published' => true,
                    'published_at' => now(),
                    'is_featured_on_dashboard' => $this->shouldBeFeatured($category->slug),
                ]);
            }
        }
    }

    private function generateArticleTitle(string $categoryTitle, int $index): string
    {
        $titles = [
            "Основы {$categoryTitle}: полное руководство",
            "Важные аспекты {$categoryTitle}",
            "Практические советы по {$categoryTitle}",
        ];

        return $titles[$index] ?? "Статья о {$categoryTitle}";
    }

    private function generateContent(): string
    {
        return "Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.";
    }

    private function shouldBeFeatured(string $categorySlug): bool
    {
        // Выбираем несколько категорий для "Факта дня"
        $featuredCategories = [
            'raschet-he',
            'tipy-insulinov',
            'dieta-pri-d2t',
        ];

        return in_array($categorySlug, $featuredCategories);
    }
} 