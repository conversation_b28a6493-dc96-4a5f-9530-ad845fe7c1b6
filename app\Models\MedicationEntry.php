<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MedicationEntry extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'medication_name',
        'dose',
        'unit',
        'medication_type',
        'taken_at',
        'notes',
    ];

    protected $casts = [
        'taken_at' => 'datetime',
        'dose' => 'float',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
