<?php

namespace App\Http\Controllers;

use App\Models\GlucoseReading;
use Illuminate\Http\Request;
use Illuminate\Http\JsonResponse;

class GlucoseReadingController extends Controller
{
    public function store(Request $request): JsonResponse
    {
        $validated = $request->validate([
            'value' => 'required|numeric|min:0',
            'unit' => 'required|string|in:mmol/L,mg/dL',
            'measured_at' => 'required|date',
            'reading_type' => 'nullable|string',
        ]);

        $glucoseReading = GlucoseReading::create([
            ...$validated,
            'user_id' => auth()->id(),
        ]);

        return response()->json([
            'message' => 'Замер успешно добавлен',
            'data' => $glucoseReading
        ]);
    }

    public function destroy(GlucoseReading $glucoseReading): JsonResponse
    {
        if ($glucoseReading->user_id !== auth()->id()) {
            return response()->json(['message' => 'Unauthorized'], 403);
        }

        $glucoseReading->delete();

        return response()->json(['message' => 'Замер успешно удален']);
    }
} 