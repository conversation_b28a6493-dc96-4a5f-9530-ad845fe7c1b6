<div class="relative">
    <div wire:loading.class="opacity-50 pointer-events-none" class="transition-opacity">
        @if ($selectedArticleId)
            <div>
                <button wire:click="backToList" class="mb-4 inline-flex items-center px-4 py-2 bg-gray-200 border border-transparent rounded-md font-semibold text-xs text-gray-800 uppercase tracking-widest hover:bg-gray-300 active:bg-gray-400 focus:outline-none focus:border-gray-900 focus:ring ring-gray-300 disabled:opacity-25 transition ease-in-out duration-150">
                    &larr; Назад к списку
                </button>
                <h1 class="text-3xl font-bold text-gray-800 mb-2">{{ $selectedArticleTitle }}</h1>
                <div class="text-sm text-gray-500 mb-4">
                    <span>Опубликовано: {{ $selectedArticleDate }}</span> |
                    <span>Категория: {{ $selectedArticleCategoryName }}</span>
                </div>
                <div
                    x-data="{
                        encodedContent: @js($selectedArticleContent),
                        get decodedContent() {
                            return this.encodedContent ? atob(this.encodedContent) : '';
                        }
                    }"
                    x-html="decodedContent"
                    class="prose max-w-none"
                ></div>
            </div>
        @elseif ($categoryId)
            <div class="border-b border-gray-200 pb-2 mb-4">
                <h2 class="text-2xl font-bold text-gray-800">Статьи из категории: {{ $categoryName }}</h2>
                 @if(!empty($categoryBreadcrumbs))
                    <div class="text-sm text-gray-500 mt-1">
                        @foreach ($categoryBreadcrumbs as $crumb)
                            <span>{{ $crumb['name'] }}</span>
                            @if (!$loop->last)
                                <span class="mx-2">/</span>
                            @endif
                        @endforeach
                    </div>
                @endif
            </div>

            @forelse ($articles as $article)
                <div class="py-4 border-b border-gray-200 last:border-b-0">
                    <h3 class="text-xl font-semibold text-gray-800 hover:text-glucosa-accent-blue transition">
                        <a href="#" wire:click.prevent="showArticle(@js($article->id))">{{ $article->title }}</a>
                    </h3>
                    <p class="text-gray-600 mt-2">{{ Str::limit(strip_tags($article->content), 150) }}</p>
                    <a href="#" wire:click.prevent="showArticle(@js($article->id))" class="text-glucosa-accent-blue hover:underline mt-2 inline-block">Читать далее...</a>
                </div>
            @empty
                <p class="text-gray-500">В этой категории пока нет статей.</p>
            @endforelse

            <div class="mt-8">
                {{ $articles->links() }}
            </div>
        @else
            <div class="text-center py-12">
                <svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
                    <path vector-effect="non-scaling-stroke" stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 13h6m-3-3v6m-9 1V7a2 2 0 012-2h14a2 2 0 012 2v10a2 2 0 01-2 2H5a2 2 0 01-2-2zm16-7H5" />
                </svg>
                <h3 class="mt-2 text-sm font-medium text-gray-900">Выберите категорию</h3>
                <p class="mt-1 text-sm text-gray-500">Чтобы увидеть список статей, выберите одну из категорий слева.</p>
            </div>
        @endif
    </div>

    <div wire:loading.class.remove="hidden" class="hidden absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
        <div class="animate-spin rounded-full h-16 w-16 border-t-2 border-b-2 border-glucosa-accent-blue"></div>
    </div>
</div> 