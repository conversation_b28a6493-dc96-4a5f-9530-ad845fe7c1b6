<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class GlucoseReading extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'value',
        'unit',
        'measured_at',
        'reading_type',
        'notes',
    ];

    protected $casts = [
        'measured_at' => 'datetime',
        'value' => 'float',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
