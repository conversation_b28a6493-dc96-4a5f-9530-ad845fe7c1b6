<?php

use Illuminate\Support\Facades\Route;

Route::get('/', [App\Http\Controllers\HomeController::class, 'index'])->name('home');

Route::view('dashboard', 'dashboard')
    ->middleware(['auth', 'verified'])
    ->name('dashboard');

Route::view('profile', 'profile')
    ->middleware(['auth'])
    ->name('profile');

Route::middleware('auth')->group(function () {
    Route::post('/glucose-readings', [App\Http\Controllers\GlucoseReadingController::class, 'store'])->name('glucose-readings.store');
    Route::delete('/glucose-readings/{glucoseReading}', [App\Http\Controllers\GlucoseReadingController::class, 'destroy'])->name('glucose-readings.destroy');
});

require __DIR__.'/auth.php';
