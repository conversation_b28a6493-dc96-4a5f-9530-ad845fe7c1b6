<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ActivityEntry extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'activity_type',
        'duration_minutes',
        'intensity',
        'performed_at',
        'notes',
    ];

    protected $casts = [
        'performed_at' => 'datetime',
        'duration_minutes' => 'integer',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
