<?php

namespace App\Livewire;

use App\Models\Article;
use App\Models\ArticleCategory;
use Livewire\Component;
use Livewire\WithPagination;
use Livewire\Attributes\On;

class ArticleList extends Component
{
    use WithPagination;

    // Свойства для выбранной категории
    public ?string $categoryId = null;
    public ?string $categoryName = null;
    public array $categoryBreadcrumbs = [];
    
    // Свойства для выбранной статьи
    public ?string $selectedArticleId = null;
    public ?string $selectedArticleTitle = null;
    public ?string $selectedArticleContent = null;
    public ?string $selectedArticleDate = null;
    public ?string $selectedArticleCategoryName = null;

    #[On('categorySelected')]
    public function selectCategory($slug)
    {
        $this->backToList(); // Сбрасываем просмотр статьи
        $category = ArticleCategory::where('slug', $slug)->with('parent.parent')->first();

        if ($category) {
            $this->categoryId = $category->id;
            $this->categoryName = $category->name;
            
            $this->categoryBreadcrumbs = [];
            $tempCategory = $category;
            while ($tempCategory) {
                array_unshift($this->categoryBreadcrumbs, ['name' => $tempCategory->name]);
                $tempCategory = $tempCategory->parent;
            }

        } else {
            $this->categoryId = null;
            $this->categoryName = null;
            $this->categoryBreadcrumbs = [];
        }
        
        $this->resetPage();
    }

    public function showArticle($articleId)
    {
        $article = Article::with('category')->find($articleId);
        if ($article) {
            $this->selectedArticleId = $article->id;
            $this->selectedArticleTitle = $article->title;
            $this->selectedArticleContent = base64_encode($article->content);
            $this->selectedArticleDate = $article->created_at->format('d.m.Y');
            $this->selectedArticleCategoryName = $article->category->name;
        }
    }

    public function backToList()
    {
        $this->selectedArticleId = null;
        $this->selectedArticleTitle = null;
        $this->selectedArticleContent = null;
        $this->selectedArticleDate = null;
        $this->selectedArticleCategoryName = null;
    }

    public function render()
    {
        $articles = collect();

        if ($this->selectedArticleId) {
            // Data is already in public properties, no need to fetch anything here.
        } elseif ($this->categoryId) {
            $articles = Article::where('article_category_id', $this->categoryId)
                ->latest()
                ->paginate(10);
        }

        return view('livewire.article-list', [
            'articles' => $articles,
        ]);
    }
} 