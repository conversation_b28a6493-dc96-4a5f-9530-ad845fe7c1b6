<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class ArticleCategory extends Model
{
    use HasUuids;

    protected $fillable = [
        'name',
        'slug',
        'parent_id',
        'description',
    ];

    public function parent(): BelongsT<PERSON>
    {
        return $this->belongsTo(ArticleCategory::class, 'parent_id');
    }

    public function children(): HasMany
    {
        return $this->hasMany(ArticleCategory::class, 'parent_id');
    }

    public function articles(): Has<PERSON><PERSON>
    {
        return $this->hasMany(Article::class);
    }
}
