// tailwind.config.js

import defaultTheme from 'tailwindcss/defaultTheme';
import forms from '@tailwindcss/forms';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './vendor/laravel/framework/src/Illuminate/Pagination/resources/views/*.blade.php',
        './storage/framework/views/*.php',
        './resources/views/**/*.blade.php',
        './resources/js/**/*.js', // Убедись, что этот путь включен, если есть JS с классами Tailwind
    ],

    theme: {
        extend: {
            fontFamily: {
                sans: ['Figtree', ...defaultTheme.fontFamily.sans],
            },
            // НАЧАЛО: Добавленные пользовательские цвета Glucosa
            colors: {
                'glucosa-green': '#7CC18C',
                'glucosa-orange': '#F8A078',
                'glucosa-dark': '#3D4D54',
                'glucosa-light-blue': '#E8F2F3',
                'glucosa-gray': '#CBCDD0',
                'glucosa-text-primary': '#333333', // Основной цвет текста
                'glucosa-text-secondary': '#555555', // Вторичный цвет текста
                'glucosa-accent-blue': '#007bff', // Акцентный синий
            },
            // КОНЕЦ: Добавленные пользовательские цвета Glucosa
        },
    },

    plugins: [forms], // Убедись, что здесь используется 'forms' как переменная, а не напрямую require
};