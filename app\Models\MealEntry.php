<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Concerns\HasUuids;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class MealEntry extends Model
{
    use HasUuids;

    protected $fillable = [
        'user_id',
        'meal_type',
        'description',
        'carbohydrates_grams',
        'bread_units',
        'consumed_at',
        'notes',
    ];

    protected $casts = [
        'consumed_at' => 'datetime',
        'carbohydrates_grams' => 'float',
        'bread_units' => 'float',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }
}
