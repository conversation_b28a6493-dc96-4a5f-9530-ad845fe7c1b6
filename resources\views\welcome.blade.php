<!DOCTYPE html>
<html lang="{{ str_replace('_', '-', app()->getLocale()) }}">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <title>Glucosa</title>

    <link rel="preconnect" href="https://fonts.bunny.net">
    <link href="https://fonts.bunny.net/css?family=figtree:400,600&display=swap" rel="stylesheet" />

    @vite(['resources/css/app.css', 'resources/js/app.js'])
    @livewireStyles
</head>

<body class="antialiased font-sans bg-glucosa-light-blue relative overflow-x-hidden flex flex-col min-h-screen">
    <div class="flex-grow">
    <!-- Логотип на заднем фоне -->
    

    <nav class="bg-white text-glucosa-dark shadow-md w-full z-50">
        <div class="max-w-7xl mx-auto flex flex-col sm:flex-row items-stretch sm:items-center h-auto sm:h-16 px-2 sm:px-4 gap-2 sm:gap-0">
            <div class="text-xl sm:text-2xl font-extrabold tracking-widest bg-gradient-to-r from-glucosa-green to-glucosa-orange text-white px-3 sm:px-4 py-1 rounded mb-2 sm:mb-0 mr-0 sm:mr-8 flex-shrink-0 flex items-center justify-center">GLUCOSA</div>
            <div class="flex flex-col sm:flex-row w-full sm:w-auto space-y-2 sm:space-y-0 space-x-0 sm:space-x-2 items-stretch sm:items-center">
                @foreach($types as $type)
                    <button id="tab-type-{{ $loop->iteration }}"
                            class="tab-button w-full sm:w-auto px-6 py-2 font-bold text-lg rounded-t focus:outline-none transition shadow-sm {{ $loop->first ? 'bg-gradient-to-r from-glucosa-green to-glucosa-orange text-white' : 'text-gray-600 hover:bg-gradient-to-r hover:from-glucosa-green hover:to-glucosa-orange hover:text-white' }}"
                            data-type-id="{{ $loop->iteration }}">
                        {{ $type->name }}
                    </button>
                @endforeach
                <input type="search" placeholder="Поиск..." class="w-full sm:w-[260px] ml-0 sm:ml-6 px-4 py-2 rounded border border-gray-300 text-black focus:outline-none focus:ring-2 focus:ring-[#4eb6b6] bg-white mt-2 sm:mt-0" >
                <button class="w-full sm:w-auto ml-0 sm:ml-6 px-6 py-2 font-bold text-lg rounded border border-gray-300 text-gray-700 hover:bg-gray-100 transition shadow-sm mt-2 sm:mt-0">Личный кабинет</button>
            </div>
            <div class="flex-1 hidden sm:block"></div>
        </div>
    </nav>
    <!-- Подменю -->
    <div class="w-full bg-gray-100 border-b border-gray-200">
        <div class="max-w-7xl mx-auto px-2 sm:px-4">
            @foreach($types as $type)
                <nav id="submenu-type-{{ $loop->iteration }}" class="submenu-block flex flex-col sm:flex-row flex-wrap items-center justify-center h-auto gap-3 sm:gap-4 py-4 {{ $loop->first ? '' : 'hidden' }}">
                    @foreach($type->children as $category)
                        <a href="#"
                           class="submenu-item text-[#232323] font-bold text-lg px-6 sm:px-8 py-3 sm:py-4 rounded-xl bg-white/90 shadow hover:bg-white transition w-full sm:w-auto text-center"
                           data-category="{{ $category->slug }}">
                            {{ $category->name }}
                        </a>
                    @endforeach
                </nav>
            @endforeach
        </div>
    </div>

    <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        @guest
        <div class="bg-white shadow-lg rounded-lg p-6 mb-8">
            <h2 class="text-2xl font-bold text-center text-glucosa-dark mb-3">
                Ваш личный помощник: получите полный контроль над диабетом!
            </h2>
            <p class="text-base text-center text-gray-600 mb-6">
                Забудьте о сложностях. Glucosa предлагает умный дневник, персонализированные напоминания и наглядную статистику — это просто. Присоединяйтесь, чтобы управлять своим здоровьем эффективно!
            </p>
            <div class="flex justify-center flex-col sm:flex-row gap-3">
                <a href="{{ route('register') }}" class="bg-glucosa-green text-white px-6 py-2 rounded-full text-lg font-bold shadow-lg hover:opacity-90 transition-opacity text-center">
                    Создать аккаунт
                </a>
                <a href="{{ route('login') }}" class="bg-white text-glucosa-dark border border-glucosa-dark px-6 py-2 rounded-full text-lg font-bold shadow-md hover:bg-glucosa-dark hover:text-white transition-colors text-center">
                    Войти
                </a>
            </div>
        </div>
        @endguest

        <div class="flex flex-col md:flex-row gap-6 md:gap-8">
            <div id="left-column-categories" class="w-full md:w-1/3 lg:w-1/4 bg-white shadow-md rounded-lg p-4 min-h-[400px]" wire:ignore>
                <div class="border border-gray-200 rounded-lg">
                    <h2 class="w-full text-center px-6 py-3 text-xl font-bold text-white rounded-t-lg bg-gradient-to-r from-glucosa-green to-glucosa-orange shadow-sm">Категории</h2>
                    <div id="subcategories-container" class="p-4">
                        <p class="text-gray-600">Выберите категорию из меню выше</p>
                    </div>
                </div>

                <div class="mt-8 p-4 border border-gray-200 rounded-lg">
                    <h3 class="font-bold text-center text-glucosa-dark mb-3">Поддержите наш проект</h3>
                    <p class="text-sm text-center text-gray-600 mb-4">
                        Ваш вклад помогает нам создавать качественный контент и поддерживать работу платформы. Каждое пожертвование имеет значение!
                    </p>
                    <a href="#" class="block w-full text-center bg-gradient-to-r from-glucosa-green to-glucosa-orange text-white px-4 py-2 rounded-lg font-bold shadow-md hover:opacity-90 transition-opacity">
                        Сделать вклад
                    </a>
                </div>
            </div>
            <div class="w-full md:w-2/3 lg:w-3/4 bg-white shadow-md rounded-lg p-6 min-h-[400px]">
                @livewire('article-list')
            </div>
        </div>
    </div>
    </div>
    @livewireScripts
    <script>
    document.addEventListener('DOMContentLoaded', function() {
        const tabButtons = document.querySelectorAll('.tab-button');
        const submenus = document.querySelectorAll('.submenu-block');
        const subcategoriesContainer = document.getElementById('subcategories-container');
        const subcategoriesData = @json($categoriesJs);

        function selectLeftCategory(slug) {
            Livewire.dispatch('categorySelected', { slug: slug });

            document.querySelectorAll('.left-category-item').forEach(item => {
                const isActive = item.dataset.slug === slug;
                
                // Remove all potentially active classes to reset the state
                item.classList.remove('bg-gradient-to-r', 'from-glucosa-green/30', 'to-glucosa-orange/30', 'shadow-md');

                // Apply a more prominent, but still readable, active state
                if (isActive) {
                    item.classList.add('bg-gradient-to-r', 'from-glucosa-green/30', 'to-glucosa-orange/30', 'shadow-md');
                }
            });
        }

        function updateSubcategories(categorySlug) {
            const items = subcategoriesData[categorySlug] || [];
            
            document.querySelectorAll('.submenu-item').forEach(item => {
                const shouldBeActive = item.dataset.category === categorySlug;
                item.classList.toggle('bg-gradient-to-r', shouldBeActive);
                item.classList.toggle('from-glucosa-green', shouldBeActive);
                item.classList.toggle('to-glucosa-orange', shouldBeActive);
                item.classList.toggle('text-white', shouldBeActive);
                item.classList.toggle('font-bold', shouldBeActive);
                item.classList.toggle('shadow', shouldBeActive);
                item.classList.toggle('bg-white/90', !shouldBeActive);
                item.classList.toggle('text-[#232323]', !shouldBeActive);
            });

            if (items.length === 0) {
                subcategoriesContainer.innerHTML = '<p class="text-gray-600">Нет подкатегорий.</p>';
                selectLeftCategory(null);
                return;
            }

            const html = items.map(item => `
                <a href="#" data-slug="${item.slug}" class="left-category-item flex items-center gap-3 p-3 rounded-lg hover:bg-gradient-to-r from-glucosa-green/5 to-glucosa-orange/5 text-glucosa-dark group transition-all duration-300">
                    <div class="flex-shrink-0 w-10 h-10 flex items-center justify-center rounded-lg bg-gradient-to-r from-glucosa-green/10 to-glucosa-orange/10 group-hover:from-glucosa-green/20 group-hover:to-glucosa-orange/20 text-glucosa-dark transition-colors duration-300">
                        ${item.icon_svg || `<svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M13 16h-1v-4h-1m1-4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path></svg>`}
                    </div>
                    <div class="flex-grow">
                        <span class="font-medium block">${item.title}</span>
                    </div>
                </a>
            `).join('<div class="h-1 w-full bg-gray-50"></div>');

            subcategoriesContainer.innerHTML = `<div class="space-y-1 -mx-3">${html}</div>`;

            const firstSubCategory = subcategoriesContainer.querySelector('.left-category-item');
            if (firstSubCategory) {
                selectLeftCategory(firstSubCategory.dataset.slug);
            } else {
                selectLeftCategory(null);
            }
        }

        function activateTab(typeId) {
            tabButtons.forEach(button => {
                const isButtonActive = button.dataset.typeId === typeId;
                button.classList.toggle('bg-gradient-to-r', isButtonActive);
                button.classList.toggle('from-glucosa-green', isButtonActive);
                button.classList.toggle('to-glucosa-orange', isButtonActive);
                button.classList.toggle('text-white', isButtonActive);
                button.classList.toggle('bg-white/10', !isButtonActive);
                button.classList.toggle('hover:from-glucosa-green', !isButtonActive);
                button.classList.toggle('hover:to-glucosa-orange', !isButtonActive);
            });
            submenus.forEach(submenu => submenu.classList.toggle('hidden', submenu.id !== `submenu-type-${typeId}`));
        }

        subcategoriesContainer.addEventListener('click', function(e) {
            const link = e.target.closest('.left-category-item');
            if (link) {
                e.preventDefault();
                selectLeftCategory(link.dataset.slug);
            }
        });

        document.querySelectorAll('.submenu-item').forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                updateSubcategories(e.target.closest('.submenu-item').dataset.category);
            });
        });

        tabButtons.forEach(button => {
            button.addEventListener('click', () => {
                const typeId = button.dataset.typeId;
                activateTab(typeId);
                const firstCategoryInNewTab = document.querySelector(`#submenu-type-${typeId} .submenu-item`);
                if (firstCategoryInNewTab) {
                    updateSubcategories(firstCategoryInNewTab.dataset.category);
                } else {
                    subcategoriesContainer.innerHTML = '<p class="text-gray-600">Нет категорий.</p>';
                    selectLeftCategory(null);
                }
            });
        });

        function init() {
            activateTab('1');
            const firstCategory = document.querySelector('#submenu-type-1 .submenu-item');
            if (firstCategory) {
                updateSubcategories(firstCategory.dataset.category);
            }
        }

        init();
    });
    </script>
    <x-footer />
</body>
</html>
